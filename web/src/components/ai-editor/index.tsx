// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useState, useCallback, useEffect } from "react";
import {
  EditorRoot,
  EditorContent,
  EditorCommand,
  EditorCommandEmpty,
  Editor<PERSON>ommand<PERSON><PERSON>,
  EditorCommandList,
  type EditorInstance,
  type JSONContent,
  handleCommandNavigation,
  handleImageDrop,
  handleImagePaste,
} from "novel";
import { useDebouncedCallback } from "use-debounce";

// 导入我们的扩展和组件
import { aiEditorExtensions } from "./extensions";
import { AIToolbar } from "./ai-toolbar";
import { AIAssistant } from "./ai-assistant";
import { aiSuggestionItems } from "./slash-command";

// 样式
import "./styles.css";

export interface AIEditorProps {
  initialContent?: any;
  onContentChange?: (content: JSONContent) => void;
  onMarkdownChange?: (markdown: string) => void;
  placeholder?: string;
  className?: string;
}

export function AIEditor({
  initialContent,
  onContentChange,
  onMarkdownChange,
  placeholder = "开始写作，选中文字体验 AI 功能...",
  className = "",
}: AIEditorProps) {
  // 编辑器状态
  const [editor, setEditor] = useState<EditorInstance | null>(null);
  const [isAIOpen, setIsAIOpen] = useState(false);
  const [selectedText, setSelectedText] = useState("");
  const [aiSuggestion, setAISuggestion] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // 防抖更新函数
  const debouncedUpdate = useDebouncedCallback(
    (editor: EditorInstance) => {
      // 获取 JSON 内容
      const jsonContent = editor.getJSON();
      onContentChange?.(jsonContent);

      // 获取 Markdown 内容
      if (onMarkdownChange && editor.storage.markdown) {
        const markdown = editor.storage.markdown.getMarkdown();
        onMarkdownChange(markdown);
      }
    },
    300
  );

  // AI 文本生成函数
  const generateAIText = useCallback(async (prompt: string, context?: string) => {
    setIsLoading(true);
    try {
      // 模拟 AI API 调用
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟 AI 响应
      const responses = [
        "这是一个很有趣的想法。让我们深入探讨一下这个概念的各个方面。",
        "基于你的描述，我建议从以下几个角度来分析这个问题：",
        "这个主题确实值得深入研究。我们可以从历史背景开始，然后分析现状。",
        "你提到的观点很有见地。让我为你扩展一些相关的思考。",
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)] || "AI 响应生成中...";
      setAISuggestion(randomResponse);

    } catch (error) {
      console.error("AI 生成失败:", error);
      setAISuggestion("抱歉，AI 生成失败，请稍后重试。");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 处理文本选择
  const handleTextSelection = useCallback(() => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    if (from === to) {
      setSelectedText("");
      return;
    }

    const text = editor.state.doc.textBetween(from, to);
    setSelectedText(text);
  }, [editor]);

  // 处理 AI 按钮点击
  const handleAIClick = useCallback(() => {
    setIsAIOpen(true);
  }, []);

  // 处理 AI 事件监听
  useEffect(() => {
    const handleAIAssistantTrigger = () => {
      setIsAIOpen(true);
    };

    const handleAIAssistantOpen = () => {
      setIsAIOpen(true);
    };

    const handleAIImproveText = async () => {
      if (selectedText) {
        await generateAIText(`请帮我改进这段文字：${selectedText}`);
      }
    };

    const handleAIExpandContent = async () => {
      if (selectedText) {
        await generateAIText(`请帮我扩展这段内容：${selectedText}`);
      }
    };

    const handleAISummarizeContent = async () => {
      if (selectedText) {
        await generateAIText(`请帮我总结这段内容的要点：${selectedText}`);
      }
    };

    // 添加事件监听
    document.addEventListener("ai-assistant-trigger", handleAIAssistantTrigger);
    document.addEventListener("ai-assistant-open", handleAIAssistantOpen);
    document.addEventListener("ai-improve-text", handleAIImproveText);
    document.addEventListener("ai-expand-content", handleAIExpandContent);
    document.addEventListener("ai-summarize-content", handleAISummarizeContent);

    return () => {
      // 清理事件监听
      document.removeEventListener("ai-assistant-trigger", handleAIAssistantTrigger);
      document.removeEventListener("ai-assistant-open", handleAIAssistantOpen);
      document.removeEventListener("ai-improve-text", handleAIImproveText);
      document.removeEventListener("ai-expand-content", handleAIExpandContent);
      document.removeEventListener("ai-summarize-content", handleAISummarizeContent);
    };
  }, [selectedText, generateAIText]);

  // 插入 AI 生成的文本
  const insertAIText = useCallback((text: string) => {
    if (!editor) return;

    editor.chain().focus().insertContent(text).run();
    setAISuggestion("");
    setIsAIOpen(false);
  }, [editor]);

  // 替换选中的文本
  const replaceSelectedText = useCallback((text: string) => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    editor.chain().focus().deleteRange({ from, to }).insertContent(text).run();
    setAISuggestion("");
    setIsAIOpen(false);
  }, [editor]);

  return (
    <div className={`relative w-full min-h-[400px] border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden ${className}`}>
      <EditorRoot>
        <EditorContent
          immediatelyRender={false}
          initialContent={initialContent}
          extensions={aiEditorExtensions}
          className="w-full h-full"
          editorProps={{
            handleDOMEvents: {
              keydown: (_view, event) => handleCommandNavigation(event),
            },
            handlePaste: (view, event) =>
              handleImagePaste(view, event, () => Promise.resolve("")),
            handleDrop: (view, event, _slice, moved) =>
              handleImageDrop(view, event, moved, () => Promise.resolve("")),
            attributes: {
              class: "prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none min-h-[400px]",
              "data-placeholder": placeholder,
            },
          }}
          onCreate={({ editor }) => {
            setEditor(editor);
          }}
          onUpdate={({ editor }) => {
            debouncedUpdate(editor);
          }}
          onSelectionUpdate={handleTextSelection}
        >
          {/* Slash 命令菜单 */}
          <EditorCommand className="z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all">
            <EditorCommandEmpty className="px-2 text-gray-500 dark:text-gray-400">
              没有找到相关命令
            </EditorCommandEmpty>
            <EditorCommandList>
              {aiSuggestionItems.map((item) => (
                <EditorCommandItem
                  value={item.title}
                  onCommand={(val) => item.command?.(val)}
                  className="flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700"
                  key={item.title}
                >
                  <div className="flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    {item.icon}
                  </div>
                  <div>
                    <p className="font-medium">{item.title}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {item.description}
                    </p>
                  </div>
                </EditorCommandItem>
              ))}
            </EditorCommandList>
          </EditorCommand>

          {/* AI 工具栏 */}
          <AIToolbar
            editor={editor}
            onAIClick={handleAIClick}
            isLoading={isLoading}
          />
        </EditorContent>
      </EditorRoot>

      {/* AI 助手面板 */}
      {isAIOpen && (
        <AIAssistant
          selectedText={selectedText}
          suggestion={aiSuggestion}
          isLoading={isLoading}
          onGenerate={generateAIText}
          onInsert={insertAIText}
          onReplace={replaceSelectedText}
          onClose={() => setIsAIOpen(false)}
        />
      )}
    </div>
  );
}

export default AIEditor;
