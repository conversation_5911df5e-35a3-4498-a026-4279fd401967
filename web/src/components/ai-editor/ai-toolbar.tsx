// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useState, useEffect } from "react";
import { 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough,
  Heading1,
  Heading2,
  List,
  ListOrdered,
  Quote,
  Code,
  Sparkles,
  Loader2,
} from "lucide-react";
import { EditorBubble, type EditorInstance } from "novel";
import { Button } from "~/components/ui/button";
import { Separator } from "~/components/ui/separator";

interface AIToolbarProps {
  editor: EditorInstance | null;
  onAIGenerate: (prompt: string, context?: string) => Promise<void>;
  isLoading: boolean;
}

export function AIToolbar({ editor, onAIGenerate, isLoading }: AIToolbarProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!editor) return;

    const updateVisibility = () => {
      const { selection } = editor.state;
      const hasSelection = !selection.empty;
      setIsVisible(hasSelection);
    };

    // 监听选择变化
    editor.on("selectionUpdate", updateVisibility);
    editor.on("transaction", updateVisibility);

    return () => {
      editor.off("selectionUpdate", updateVisibility);
      editor.off("transaction", updateVisibility);
    };
  }, [editor]);

  if (!editor || !isVisible) return null;

  const handleFormatClick = (command: () => void) => {
    command();
    editor.commands.focus();
  };

  const handleAIClick = async () => {
    const { from, to } = editor.state.selection;
    const selectedText = editor.state.doc.textBetween(from, to);
    
    if (selectedText.trim()) {
      await onAIGenerate(`请帮我改进这段文字：${selectedText}`);
    }
  };

  return (
    <EditorBubble
      tippyOptions={{
        placement: "top",
        duration: 100,
      }}
      className="ai-toolbar-bubble"
    >
      <div className="flex items-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-1">
        {/* 文本格式化按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleBold().run())}
          className={editor.isActive("bold") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Bold className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleItalic().run())}
          className={editor.isActive("italic") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Italic className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleUnderline().run())}
          className={editor.isActive("underline") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Underline className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleStrike().run())}
          className={editor.isActive("strike") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Strikethrough className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 标题按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleHeading({ level: 1 }).run())}
          className={editor.isActive("heading", { level: 1 }) ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Heading1 className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleHeading({ level: 2 }).run())}
          className={editor.isActive("heading", { level: 2 }) ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Heading2 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 列表按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleBulletList().run())}
          className={editor.isActive("bulletList") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <List className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleOrderedList().run())}
          className={editor.isActive("orderedList") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* 其他格式按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleBlockquote().run())}
          className={editor.isActive("blockquote") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Quote className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleFormatClick(() => editor.chain().toggleCode().run())}
          className={editor.isActive("code") ? "bg-gray-100 dark:bg-gray-700" : ""}
        >
          <Code className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* AI 按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleAIClick}
          disabled={isLoading}
          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Sparkles className="h-4 w-4" />
          )}
          <span className="ml-1 text-xs">AI</span>
        </Button>
      </div>
    </EditorBubble>
  );
}
