/* AI Editor 样式 */

.ai-editor-container {
  @apply relative w-full min-h-[400px] border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden;
}

.ai-editor-content {
  @apply w-full h-full;
}

.ai-editor-prose {
  @apply prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none;
  @apply prose-headings:font-semibold prose-headings:text-gray-900 dark:prose-headings:text-gray-100;
  @apply prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed;
  @apply prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:no-underline hover:prose-a:underline;
  @apply prose-strong:text-gray-900 dark:prose-strong:text-gray-100;
  @apply prose-code:text-blue-600 dark:prose-code:text-blue-400 prose-code:bg-gray-100 dark:prose-code:bg-gray-800;
  @apply prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800;
  min-height: 400px;
}

/* 占位符样式 */
.ai-editor-prose:empty::before {
  content: attr(data-placeholder);
  @apply text-gray-400 dark:text-gray-500 pointer-events-none float-left h-0;
}

/* 列表样式 */
.ai-editor-bullet-list {
  @apply list-disc list-outside ml-6 space-y-1;
}

.ai-editor-ordered-list {
  @apply list-decimal list-outside ml-6 space-y-1;
}

/* 引用样式 */
.ai-editor-blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 py-2 my-4;
}

/* 代码样式 */
.ai-editor-code {
  @apply bg-gray-100 dark:bg-gray-800 text-blue-600 dark:text-blue-400 px-1 py-0.5 rounded text-sm;
}

/* 标题样式 */
.ai-editor-heading {
  @apply font-semibold text-gray-900 dark:text-gray-100 mt-6 mb-3;
}

.ai-editor-heading[data-level="1"] {
  @apply text-2xl;
}

.ai-editor-heading[data-level="2"] {
  @apply text-xl;
}

.ai-editor-heading[data-level="3"] {
  @apply text-lg;
}

/* 链接样式 */
.ai-editor-link {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors cursor-pointer;
}

/* 工具栏气泡样式 */
.ai-toolbar-bubble {
  @apply z-50;
}

/* 选择高亮样式 */
.ProseMirror-selectednode {
  @apply outline-none bg-blue-100 dark:bg-blue-900/30 rounded;
}

/* AI 高亮样式 */
.ai-editor-prose mark[data-color="#3b82f6"] {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-100 px-1 rounded;
}

/* 焦点样式 */
.ai-editor-content .ProseMirror:focus {
  @apply outline-none;
}

/* 拖拽样式 */
.ai-editor-prose .ProseMirror-drop-cursor {
  @apply border-l-2 border-blue-500;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .ai-editor-prose {
    @apply p-3 text-sm;
  }
  
  .ai-toolbar-bubble {
    @apply scale-90;
  }
}

/* 动画效果 */
.ai-editor-container {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.ai-loading {
  @apply opacity-50 pointer-events-none;
}

/* 错误状态 */
.ai-error {
  @apply border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20;
}

/* 成功状态 */
.ai-success {
  @apply border-green-300 dark:border-green-700 bg-green-50 dark:bg-green-900/20;
}
