/* AI Editor 样式 */

.ai-editor-container {
  @apply relative w-full min-h-[400px] border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden;
}

.ai-editor-content {
  @apply w-full h-full;
}

.ai-editor-prose {
  @apply prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none;
  @apply prose-headings:font-semibold prose-headings:text-gray-900 dark:prose-headings:text-gray-100;
  @apply prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed;
  @apply prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:no-underline hover:prose-a:underline;
  @apply prose-strong:text-gray-900 dark:prose-strong:text-gray-100;
  @apply prose-code:text-blue-600 dark:prose-code:text-blue-400 prose-code:bg-gray-100 dark:prose-code:bg-gray-800;
  @apply prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800;
  min-height: 400px;
}

/* 占位符样式 */
.ai-editor-prose:empty::before {
  content: attr(data-placeholder);
  @apply text-gray-400 dark:text-gray-500 pointer-events-none float-left h-0;
}

/* 列表样式 */
.ai-editor-bullet-list {
  @apply list-disc list-outside ml-6 space-y-1;
}

.ai-editor-ordered-list {
  @apply list-decimal list-outside ml-6 space-y-1;
}

/* 引用样式 */
.ai-editor-blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 py-2 my-4;
}

/* 代码样式 */
.ai-editor-code {
  @apply bg-gray-100 dark:bg-gray-800 text-blue-600 dark:text-blue-400 px-1 py-0.5 rounded text-sm;
}

/* 标题样式 */
.ai-editor-heading {
  @apply font-semibold text-gray-900 dark:text-gray-100 mt-6 mb-3;
}

.ai-editor-heading[data-level="1"] {
  @apply text-2xl;
}

.ai-editor-heading[data-level="2"] {
  @apply text-xl;
}

.ai-editor-heading[data-level="3"] {
  @apply text-lg;
}

/* 链接样式 */
.ai-editor-link {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors cursor-pointer;
}

/* 工具栏气泡样式 */
.ai-toolbar-bubble {
  @apply z-50;
}

/* 选择高亮样式 */
.ProseMirror-selectednode {
  @apply outline-none bg-blue-100 dark:bg-blue-900/30 rounded;
}

/* AI 高亮样式 */
.ai-editor-prose mark[data-color="#3b82f6"] {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-100 px-1 rounded;
}

/* 焦点样式 */
.ai-editor-content .ProseMirror:focus {
  @apply outline-none;
}

/* 拖拽样式 */
.ai-editor-prose .ProseMirror-drop-cursor {
  @apply border-l-2 border-blue-500;
}

/* 拖拽手柄样式 */
.drag-handle {
  position: fixed;
  opacity: 1;
  transition: opacity ease-in 0.2s;
  border-radius: 0.25rem;
  background-image: url("data:image/svg+xml,%3csvg width='10' height='10' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z' fill='%23a1a1aa'/%3e%3c/svg%3e");
  background-size: calc(0.5em + 0.375rem) calc(0.5em + 0.375rem);
  background-repeat: no-repeat;
  background-position: center;
  width: 1.2rem;
  height: 1.5rem;
  z-index: 50;
  cursor: grab;
}

.drag-handle:hover {
  @apply bg-gray-100 dark:bg-gray-700;
  transition: background-color 0.2s;
}

.drag-handle:active {
  @apply bg-gray-200 dark:bg-gray-600;
  transition: background-color 0.2s;
  cursor: grabbing;
}

.drag-handle.hide {
  opacity: 0;
  pointer-events: none;
}

/* 移动端隐藏拖拽手柄 */
@media screen and (max-width: 600px) {
  .drag-handle {
    display: none;
    pointer-events: none;
  }
}

/* 选中节点样式 */
.ProseMirror:not(.dragging) .ProseMirror-selectednode {
  outline: none !important;
  @apply bg-blue-100 dark:bg-blue-900/30;
  transition: background-color 0.2s;
  box-shadow: none;
}

/* 任务列表样式 */
.ai-editor-task-list {
  @apply list-none pl-0;
}

.ai-editor-task-item {
  @apply flex items-start gap-2 my-2;
}

.ai-editor-task-item input[type="checkbox"] {
  @apply mt-1 cursor-pointer;
}

/* 水平分割线样式 */
.ai-editor-hr {
  @apply border-t border-gray-300 dark:border-gray-600 my-4;
}

/* 代码块样式 */
.ai-editor-code-block {
  @apply bg-gray-100 dark:bg-gray-800 rounded-lg p-4 my-4 overflow-x-auto;
}

.ai-editor-code-block pre {
  @apply m-0;
}

.ai-editor-code-block code {
  @apply text-sm font-mono;
}

/* AI 高亮样式 */
.ai-editor-ai-highlight {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-100 px-1 rounded;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .ai-editor-prose {
    @apply p-3 text-sm;
  }
  
  .ai-toolbar-bubble {
    @apply scale-90;
  }
}

/* 动画效果 */
.ai-editor-container {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.ai-loading {
  @apply opacity-50 pointer-events-none;
}

/* 错误状态 */
.ai-error {
  @apply border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20;
}

/* 成功状态 */
.ai-success {
  @apply border-green-300 dark:border-green-700 bg-green-50 dark:bg-green-900/20;
}
