// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import {
  CheckSquare,
  Code,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Text,
  TextQuote,
  Sparkles,
  Wand2,
  Edit3,
  Plus,
} from "lucide-react";
import { Command, createSuggestionItems, renderItems } from "novel";

export const aiSuggestionItems = createSuggestionItems([
  {
    title: "文本段落",
    description: "开始输入普通文本内容",
    searchTerms: ["p", "paragraph", "text", "文本"],
    icon: <Text size={18} />,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleNode("paragraph", "paragraph")
        .run();
    },
  },
  {
    title: "待办列表",
    description: "创建可勾选的任务列表",
    searchTerms: ["todo", "task", "list", "check", "checkbox", "待办", "任务"],
    icon: <CheckSquare size={18} />,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleTaskList().run();
    },
  },
  {
    title: "一级标题",
    description: "创建大标题",
    searchTerms: ["title", "big", "large", "h1", "标题"],
    icon: <Heading1 size={18} />,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setNode("heading", { level: 1 })
        .run();
    },
  },
  {
    title: "二级标题",
    description: "创建中等标题",
    searchTerms: ["subtitle", "medium", "h2", "副标题"],
    icon: <Heading2 size={18} />,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setNode("heading", { level: 2 })
        .run();
    },
  },
  {
    title: "三级标题",
    description: "创建小标题",
    searchTerms: ["subtitle", "small", "h3", "小标题"],
    icon: <Heading3 size={18} />,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setNode("heading", { level: 3 })
        .run();
    },
  },
  {
    title: "无序列表",
    description: "创建项目符号列表",
    searchTerms: ["unordered", "point", "bullet", "列表"],
    icon: <List size={18} />,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleBulletList().run();
    },
  },
  {
    title: "有序列表",
    description: "创建带编号的列表",
    searchTerms: ["ordered", "number", "编号"],
    icon: <ListOrdered size={18} />,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleOrderedList().run();
    },
  },
  {
    title: "引用块",
    description: "创建引用文本",
    searchTerms: ["blockquote", "quote", "引用"],
    icon: <TextQuote size={18} />,
    command: ({ editor, range }) =>
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleNode("paragraph", "paragraph")
        .toggleBlockquote()
        .run(),
  },
  {
    title: "代码块",
    description: "插入代码片段",
    searchTerms: ["codeblock", "code", "代码"],
    icon: <Code size={18} />,
    command: ({ editor, range }) =>
      editor.chain().focus().deleteRange(range).toggleCodeBlock().run(),
  },
  // AI 功能命令
  {
    title: "AI 写作助手",
    description: "打开 AI 助手进行智能写作",
    searchTerms: ["ai", "assistant", "help", "智能", "助手"],
    icon: <Sparkles size={18} />,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).run();
      // 触发 AI 助手
      const event = new CustomEvent("ai-assistant-open", {
        detail: { position: range.from },
      });
      document.dispatchEvent(event);
    },
  },
  {
    title: "AI 改进文字",
    description: "让 AI 帮助改进当前段落",
    searchTerms: ["improve", "enhance", "改进", "优化"],
    icon: <Edit3 size={18} />,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).run();
      // 触发 AI 改进
      const event = new CustomEvent("ai-improve-text", {
        detail: { position: range.from },
      });
      document.dispatchEvent(event);
    },
  },
  {
    title: "AI 扩展内容",
    description: "让 AI 帮助扩展当前内容",
    searchTerms: ["expand", "extend", "扩展", "详细"],
    icon: <Plus size={18} />,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).run();
      // 触发 AI 扩展
      const event = new CustomEvent("ai-expand-content", {
        detail: { position: range.from },
      });
      document.dispatchEvent(event);
    },
  },
  {
    title: "AI 总结要点",
    description: "让 AI 帮助总结内容要点",
    searchTerms: ["summarize", "summary", "总结", "要点"],
    icon: <Wand2 size={18} />,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).run();
      // 触发 AI 总结
      const event = new CustomEvent("ai-summarize-content", {
        detail: { position: range.from },
      });
      document.dispatchEvent(event);
    },
  },
]);

export const aiSlashCommand = Command.configure({
  suggestion: {
    items: () => aiSuggestionItems,
    render: renderItems,
  },
});
