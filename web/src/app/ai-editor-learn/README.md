# 🤖 AI 编辑器学习项目

## 📋 项目概述

这是一个从零开始构建的 AI 编辑器学习项目，基于 Novel + TipTap + ProseMirror 技术栈。

## ✨ 功能特性

### 🎯 核心功能
- ✅ **基础编辑器** - 完整的富文本编辑功能
- ✅ **AI 工具栏** - 选中文字时自动显示，点击 AI 按钮打开助手
- ✅ **AI 助手面板** - 提供快速提示词和自定义指令
- ✅ **Slash 命令** - 输入 `/` 快速插入内容和触发 AI 功能
- ✅ **拖拽功能** - 支持段落、图片等元素的拖拽重排
- ✅ **Markdown 输出** - 实时转换为 Markdown 格式
- ✅ **响应式设计** - 适配各种屏幕尺寸

### 🤖 AI 功能
- **改进文字** - 让文字更清晰、更有说服力
- **扩展内容** - 添加更多细节和例子
- **总结要点** - 提取核心要点和关键信息
- **修正语法** - 检查并修正语法和表达
- **自定义指令** - 支持用户自定义 AI 提示词

### ⚡ 交互体验
- **智能工具栏** - 选中文字时自动显示，长度优化
- **Slash 命令** - 输入 `/` 快速访问所有功能
- **拖拽重排** - 直观的内容重新组织
- **快捷键支持** - Ctrl+K 打开 AI 助手
- **实时反馈** - 加载状态和错误处理
- **流畅动画** - 平滑的界面过渡效果

## 🏗️ 技术架构

### 三层架构设计
```
🎨 Novel (上层)     - React 组件封装 + UI 交互
⚙️ TipTap (中间层)  - 扩展系统 + API 封装  
🔧 ProseMirror (底层) - 核心引擎 + DOM 操作
```

### 核心组件
- **AIEditor** - 主编辑器组件
- **AIToolbar** - 浮动工具栏组件
- **AIAssistant** - AI 助手面板组件
- **Extensions** - 自定义扩展配置

## 🚀 使用方法

### 基础编辑
1. 在编辑器中开始写作
2. 使用标准的富文本编辑功能
3. 查看右侧的 Markdown 输出

### AI 功能
1. **选中文字** - 选中任意文字，工具栏会自动出现
2. **点击 AI 按钮** - 点击工具栏中的 AI 按钮
3. **选择操作** - 选择快速提示词或输入自定义指令
4. **应用结果** - 选择插入或替换生成的内容

### Slash 命令
1. **输入 `/`** - 在空行输入斜杠符号
2. **选择命令** - 从弹出菜单中选择所需功能
3. **快速插入** - 支持标题、列表、代码块等
4. **AI 功能** - 直接通过 slash 命令触发 AI 助手

### 拖拽功能
1. **悬停显示** - 鼠标悬停在段落左侧显示拖拽手柄
2. **拖拽移动** - 点击并拖拽重新排列内容
3. **实时预览** - 拖拽过程中显示插入位置
4. **移动端隐藏** - 小屏幕设备自动隐藏拖拽功能

### 快捷键
- `Ctrl+K` - 打开 AI 助手
- `Ctrl+Shift+A` - 快速 AI 生成（开发中）
- `/` - 打开 Slash 命令菜单

## 📁 项目结构

```
src/components/ai-editor/
├── index.tsx          # 主编辑器组件
├── extensions.ts      # TipTap 扩展配置
├── ai-toolbar.tsx     # AI 工具栏组件
├── ai-assistant.tsx   # AI 助手面板组件
├── slash-command.tsx  # Slash 命令配置
└── styles.css         # 样式文件

src/app/ai-editor-learn/
├── page.tsx           # 演示页面
└── README.md          # 项目说明
```

## 🔧 开发进度

### ✅ 已完成
- [x] 基础编辑器功能
- [x] AI 工具栏实现（优化长度限制）
- [x] AI 助手面板（修复弹出时机）
- [x] 文本选择处理
- [x] Markdown 输出
- [x] 响应式布局
- [x] 页面简化优化
- [x] Slash 命令系统
- [x] 拖拽功能实现
- [x] 样式系统完善

### 🔄 开发中
- [ ] 真实 AI API 集成
- [ ] 更多 AI 提示词模板
- [ ] 性能优化

### ⏳ 计划中
- [ ] 插件系统扩展
- [ ] 协作编辑功能
- [ ] 导出功能增强
- [ ] 性能优化

## 🎯 学习价值

这个项目展示了：

1. **现代编辑器开发** - 从零开始构建富文本编辑器
2. **三层架构实践** - Novel + TipTap + ProseMirror 的实际应用
3. **AI 功能集成** - 如何将 AI 能力无缝集成到编辑器中
4. **React 最佳实践** - Hooks、状态管理、组件设计
5. **用户体验设计** - 交互设计和界面优化

## 🐛 已知问题

1. **AI 模拟** - 当前使用模拟 AI 响应，需要集成真实 API
2. **字体加载** - Google Fonts 加载问题，不影响功能使用
3. **移动端优化** - 部分功能在移动端的体验还需要进一步优化

## 🔗 相关链接

- [Novel 编辑器](https://novel.sh/)
- [TipTap 文档](https://tiptap.dev/)
- [ProseMirror 指南](https://prosemirror.net/)
- [DeerFlow 项目](https://github.com/bytedance/deer-flow)

## 📝 更新日志

### v1.2.0 (2025-01-25)
- ✨ 新增 Slash 命令系统
- ✨ 新增拖拽功能支持
- 🔧 修复 AI 工具栏长度限制问题
- 🔧 修复 AI 助手弹出时机问题
- 💄 完善样式系统和交互体验

### v1.1.0 (2025-01-25)
- 🔧 优化 AI 工具栏交互逻辑
- 💄 改进工具栏样式和长度控制
- 📝 完善项目文档

### v1.0.0 (2025-01-25)
- ✨ 初始版本发布
- ✅ 基础编辑器功能完成
- ✅ AI 功能集成完成
- ✅ 页面简化优化完成

---

**开发者**: AI Assistant  
**技术栈**: Next.js + React + TypeScript + Novel + TipTap + ProseMirror  
**最后更新**: 2025-01-25
