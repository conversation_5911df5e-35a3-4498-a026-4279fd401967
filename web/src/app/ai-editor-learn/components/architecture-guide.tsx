// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useState } from "react";
import { ChevronDown, ChevronRight, Code2, Layers, Sparkles, Target } from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";

interface ArchitectureSection {
  id: string;
  title: string;
  description: string;
  level: "beginner" | "intermediate" | "advanced";
  content: {
    overview: string;
    keyPoints: string[];
    codeExample: string;
    learningGoals: string[];
  };
}

const architectureSections: ArchitectureSection[] = [
  {
    id: "project-structure",
    title: "📁 项目结构设计",
    description: "学习如何组织一个完整的编辑器项目",
    level: "beginner",
    content: {
      overview: "我们从零开始构建了一个完整的 AI 编辑器，采用模块化的项目结构，每个组件都有明确的职责。",
      keyPoints: [
        "index.tsx - 主编辑器组件，负责状态管理和组件组合",
        "extensions.ts - TipTap 扩展配置，定义编辑器功能",
        "ai-toolbar.tsx - 浮动工具栏，提供格式化和 AI 功能",
        "ai-assistant.tsx - AI 助手面板，处理 AI 交互",
        "styles.css - 样式文件，定义编辑器外观"
      ],
      codeExample: `// 项目结构
src/components/ai-editor/
├── index.tsx          // 主编辑器组件
├── extensions.ts      // 扩展配置
├── ai-toolbar.tsx     // AI 工具栏
├── ai-assistant.tsx   // AI 助手
└── styles.css         // 样式文件`,
      learningGoals: [
        "理解模块化组件设计",
        "掌握职责分离原则",
        "学会组织复杂项目结构"
      ]
    }
  },
  {
    id: "extension-system",
    title: "⚙️ 扩展系统实现",
    description: "深入理解 TipTap 扩展的创建和配置",
    level: "intermediate",
    content: {
      overview: "我们创建了两个自定义扩展：AICommandExtension 和 AISlashExtension，展示了如何扩展编辑器功能。",
      keyPoints: [
        "Extension.create() - 创建自定义扩展的标准方法",
        "addKeyboardShortcuts() - 添加快捷键支持",
        "addCommands() - 定义编辑器命令",
        "自定义事件 - 使用 CustomEvent 进行组件通信",
        "扩展配置 - 组合多个扩展形成完整功能"
      ],
      codeExample: `// 自定义 AI 命令扩展
const AICommandExtension = Extension.create({
  name: "aiCommand",
  
  addKeyboardShortcuts() {
    return {
      "Mod-k": () => {
        const event = new CustomEvent("ai-assistant-trigger");
        document.dispatchEvent(event);
        return true;
      },
    };
  },
  
  addCommands() {
    return {
      setAIHighlight: () => ({ commands }) => {
        return commands.setHighlight({ color: "#3b82f6" });
      },
    };
  },
});`,
      learningGoals: [
        "掌握 TipTap 扩展开发",
        "理解快捷键系统",
        "学会自定义编辑器命令"
      ]
    }
  },
  {
    id: "state-management",
    title: "🔄 状态管理策略",
    description: "学习编辑器状态的管理和数据流控制",
    level: "intermediate",
    content: {
      overview: "编辑器使用 React Hooks 管理状态，包括编辑器实例、AI 状态、用户交互状态等。",
      keyPoints: [
        "useState - 管理组件本地状态",
        "useCallback - 优化事件处理函数",
        "useDebouncedCallback - 防抖处理内容更新",
        "事件监听 - 监听编辑器选择变化",
        "状态同步 - 保持 UI 和编辑器状态一致"
      ],
      codeExample: `// 状态管理示例
const [editor, setEditor] = useState<EditorInstance | null>(null);
const [isAIOpen, setIsAIOpen] = useState(false);
const [selectedText, setSelectedText] = useState("");

const debouncedUpdate = useDebouncedCallback(
  (editor: EditorInstance) => {
    const jsonContent = editor.getJSON();
    onContentChange?.(jsonContent);
  },
  300
);

const handleTextSelection = useCallback(() => {
  if (!editor) return;
  const { from, to } = editor.state.selection;
  const text = editor.state.doc.textBetween(from, to);
  setSelectedText(text);
}, [editor]);`,
      learningGoals: [
        "掌握 React Hooks 使用",
        "理解防抖优化技术",
        "学会编辑器事件处理"
      ]
    }
  },
  {
    id: "ai-integration",
    title: "🤖 AI 功能集成",
    description: "学习如何将 AI 功能无缝集成到编辑器中",
    level: "advanced",
    content: {
      overview: "AI 功能通过工具栏和助手面板提供，支持快速提示词和自定义指令，展示了现代编辑器的智能化设计。",
      keyPoints: [
        "EditorBubble - 浮动工具栏的实现",
        "AI 提示词系统 - 预定义的快速操作",
        "自定义指令 - 用户自定义的 AI 交互",
        "异步处理 - AI API 调用的状态管理",
        "用户体验 - 加载状态和错误处理"
      ],
      codeExample: `// AI 功能集成
const generateAIText = useCallback(async (prompt: string) => {
  setIsLoading(true);
  try {
    // 模拟 AI API 调用
    await new Promise(resolve => setTimeout(resolve, 1500));
    const response = "AI 生成的内容...";
    setAISuggestion(response);
  } catch (error) {
    console.error("AI 生成失败:", error);
  } finally {
    setIsLoading(false);
  }
}, []);

// 快速提示词
const AI_PROMPTS = [
  { 
    label: "改进文字", 
    prompt: "请帮我改进这段文字，让它更清晰、更有说服力" 
  },
  { 
    label: "扩展内容", 
    prompt: "请帮我扩展这段内容，添加更多细节和例子" 
  },
];`,
      learningGoals: [
        "掌握异步状态管理",
        "理解用户体验设计",
        "学会 AI 功能集成"
      ]
    }
  }
];

export function ArchitectureGuide() {
  const [openSections, setOpenSections] = useState<string[]>(["project-structure"]);

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case "beginner": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "intermediate": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "advanced": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold mb-2 flex items-center justify-center">
          <Layers className="h-6 w-6 mr-2" />
          AI 编辑器架构指南
        </h2>
        <p className="text-muted-foreground">
          从零开始构建 AI 编辑器的完整学习指南
        </p>
      </div>

      {/* 学习目标 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>学习目标</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium">技术能力</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 掌握 TipTap 扩展开发</li>
                <li>• 理解编辑器状态管理</li>
                <li>• 学会 AI 功能集成</li>
                <li>• 掌握组件化设计</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">实践成果</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 完整的 AI 编辑器项目</li>
                <li>• 可复用的组件库</li>
                <li>• 扩展开发经验</li>
                <li>• 架构设计思维</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 架构详解 */}
      <div className="space-y-4">
        {architectureSections.map((section) => (
          <Card key={section.id}>
            <Collapsible
              open={openSections.includes(section.id)}
              onOpenChange={() => toggleSection(section.id)}
            >
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Button variant="ghost" size="sm" className="p-0 h-auto">
                        {openSections.includes(section.id) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                      <div>
                        <CardTitle className="text-left">{section.title}</CardTitle>
                        <CardDescription className="text-left">
                          {section.description}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge className={getLevelColor(section.level)}>
                      {section.level}
                    </Badge>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                <CardContent>
                  <Tabs defaultValue="overview" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="overview">概述</TabsTrigger>
                      <TabsTrigger value="points">要点</TabsTrigger>
                      <TabsTrigger value="code">代码</TabsTrigger>
                      <TabsTrigger value="goals">目标</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="overview" className="mt-4">
                      <p className="text-sm text-muted-foreground">
                        {section.content.overview}
                      </p>
                    </TabsContent>
                    
                    <TabsContent value="points" className="mt-4">
                      <ul className="space-y-2">
                        {section.content.keyPoints.map((point, index) => (
                          <li key={index} className="flex items-start">
                            <Sparkles className="h-4 w-4 mr-2 mt-0.5 text-blue-500" />
                            <span className="text-sm">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </TabsContent>
                    
                    <TabsContent value="code" className="mt-4">
                      <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                        <code>{section.content.codeExample}</code>
                      </pre>
                    </TabsContent>
                    
                    <TabsContent value="goals" className="mt-4">
                      <ul className="space-y-2">
                        {section.content.learningGoals.map((goal, index) => (
                          <li key={index} className="flex items-start">
                            <Target className="h-4 w-4 mr-2 mt-0.5 text-green-500" />
                            <span className="text-sm">{goal}</span>
                          </li>
                        ))}
                      </ul>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        ))}
      </div>
    </div>
  );
}
