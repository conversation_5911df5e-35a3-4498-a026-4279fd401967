{"output": {"filePath": "/home/<USER>/github/deer-flow/repomix-output.txt", "style": "plain", "parsableStyle": false, "headerText": "", "instructionFilePath": "", "fileSummary": true, "directoryStructure": true, "removeComments": true, "removeEmptyLines": true, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": true, "includeEmptyDirectories": false, "compress": true}, "include": [], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": ["web/public/", "tests/", "README_ru.md", "README_es.md", "README_de.md", "README_pt.md"]}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}, "cwd": "/home/<USER>/github/deer-flow"}